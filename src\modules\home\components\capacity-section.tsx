import Link from 'next/link';
import getCategory from '@/actions/category/get-category';
import type { ICategory } from '@/types/category';

export default async function CapacitySection() {
  const res = await getCategory();

  const categories: ICategory[] = (res?.data ?? []).filter(cat => cat.slug !== "about-us");

  return (
    <section className="relative py-10 sm:py-12 md:py-16 lg:py-20 overflow-hidden bg-gradient-to-b from-secondary/5 to-primary/10">
      <div className="relative z-10 container mx-auto px-3 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
            OUR CAPACITY
          </h2>
          <div className="w-20 sm:w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-4 sm:mt-6 rounded-full" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10">
          {categories.map((cat) => {
            const imgUrl =
              cat.image || '/images/helicopter.png';

            return (
              <div
                key={cat.id}
                className="relative group overflow-hidden rounded-xl shadow-lg md:hover:shadow-2xl transition-shadow duration-300"
                aria-label={`${cat.name} capacity card`}
              >
                <div className="relative h-60 sm:h-72 md:h-80 lg:h-96 bg-gradient-to-br from-gray-700 to-gray-500 md:group-hover:scale-[1.02] transition-transform duration-500 ease-out will-change-transform">
                  <div
                    className="absolute inset-0 bg-cover bg-center filter duration-500 "
                    style={{ backgroundImage: `url(${imgUrl})` }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent to-gray-900/80" />

                  <div className="absolute bottom-0 w-full px-4 sm:px-5 md:px-6 py-4 sm:py-5 bg-gradient-to-t from-black/80 to-transparent">
                    <h3 className="text-white text-xl sm:text-2xl md:text-3xl font-semibold text-center drop-shadow">
                      {cat.name}
                    </h3>
                    <p className="mt-1 text-white/90 text-xs sm:text-sm text-center leading-relaxed">
                      Trusted solutions in {cat.name}.
                    </p>
                  </div>
                </div>
                <div className="h-1.5 sm:h-2 w-full bg-gradient-to-r from-primary to-secondary" />

                <Link
                  href={`/${cat.slug}`}
                  className="absolute inset-0"
                  aria-label={`Learn more about ${cat.name}`}
                />
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};


// 'use client';

// import Link from 'next/link';

// const cardData = [
//   {
//     title: 'AIRLINES',
//     description: 'Commercial aircraft parts and comprehensive airline support solutions',
//     imageUrl: '/images/airline.png',
//     href: '/aircraft/airlines',
//     gradientFrom: 'from-blue-900',
//     gradientTo: 'to-blue-700',
//     overlayOpacity: 'bg-black/30',
//     hoverOverlayOpacity: 'group-hover:bg-black/20',
//   },
//   {
//     title: 'HELICOPTERS',
//     description: 'Rotorcraft parts and specialized helicopter maintenance solutions',
//     imageUrl: '/images/helicopter.png',
//     href: '/aircraft/helicopters',
//     gradientFrom: 'from-yellow-600',
//     gradientTo: 'to-orange-600',
//     overlayOpacity: 'bg-black/30',
//     hoverOverlayOpacity: 'group-hover:bg-black/20',
//   },
//   {
//     title: 'DEFENCE',
//     description: 'Military aircraft support with security-cleared processes and documentation',
//     imageUrl: '/images/defense.png',
//     href: '/aircraft/defence',
//     gradientFrom: 'from-green-800',
//     gradientTo: 'to-gray-800',
//     overlayOpacity: 'bg-black/40',
//     hoverOverlayOpacity: 'group-hover:bg-black/30',
//   },
// ];

// const CapacitySection = () => {
//   return (
//     <section className="py-14 bg-gray-50">
//       <div className="container mx-auto px-6">
//         {/* Section Header */}
//         <div className="text-center mb-16">
//           <h2 className="text-4xl text-center font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent mb-2">
//             OUR CAPACITY
//           </h2>
//           <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-6 rounded-full mb-6" />
//         </div>

//         {/* Cards Grid */}
//         <div className="grid md:grid-cols-3 gap-10">
//           {cardData.map(({ title, description, imageUrl, href, gradientFrom, gradientTo, overlayOpacity, hoverOverlayOpacity }) => (
//             <div
//               key={title}
//               className="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-shadow duration-400"
//               aria-label={`${title} capacity card`}
//             >
//               <div
//                 className={`relative h-72 bg-gradient-to-br ${gradientFrom} ${gradientTo} rounded-xl transform gpu-animation group-hover:scale-105 transition-transform duration-500 ease-in-out`}
//               >
//                 {/* Background Image with smooth zoom */}
//                 <div
//                   className="absolute inset-0 bg-cover bg-center filter brightness-90 transition-filter duration-500 group-hover:brightness-100"
//                   style={{ backgroundImage: `url(${imageUrl})` }}
//                   role="img"
//                   aria-label={`${title} background image`}
//                 />
//                 {/* Overlay */}
//                 <div
//                   className={`absolute inset-0 ${overlayOpacity} ${hoverOverlayOpacity} transition-colors duration-500 rounded-xl`}
//                 />
//                 {/* Content */}
//                 <div className="absolute inset-0 flex flex-col items-center justify-center text-center px-6">
//                   <h3 className="text-4xl font-semibold text-white drop-shadow-md mb-4 tracking-wide shadow-black">
//                     {title}
//                   </h3>
//                   <p className="text-white text-sm max-w-xs opacity-95 leading-relaxed drop-shadow-sm">
//                     {description}
//                   </p>
//                 </div>
//               </div>
//               {/* Link overlay */}
//               <Link
//                 href={href}
//                 className="absolute inset-0"
//                 aria-label={`Learn more about ${title}`}
//               />
//             </div>
//           ))}
//         </div>
//       </div>
//     </section>
//   );
// };

// export default CapacitySection;
