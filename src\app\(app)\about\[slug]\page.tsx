import getPageBySlug from '@/actions/pages/get-page-by-slug';
import getPages from '@/actions/pages/get-pages';
import HtmlContentDisplay from '@/components/html-content-display';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { IPages } from '@/types/category';
import { ChevronDown, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import React from 'react';

// interface AboutPageProps {
//     params: { slug: string };
// }

type Params = { slug: string };

export default async function AboutPage(
    { params }: { params: Promise<Params> }
) {
    const { slug } = await params;
    const page = await getPageBySlug(slug);

    if (!page?.success || !page.data) {
        notFound();
    }

    const pageData: IPages = page.data;

    const allPagesRes = await getPages();
    const allPages = allPagesRes?.data ?? [];
    const aboutPages = allPages.filter(
        (p: IPages) =>
            p.category?.slug === "about-us" && p.showOnNav
    );

    return (
        <>
            {pageData.image && (
                <section
                    className="relative md:h-[500px] h-[300px] flex items-center bg-cover bg-center"
                    style={{
                        backgroundImage: `url(${pageData.image || '/images/hero.jpg'})`,
                    }}
                >
                    <div className="relative z-10 container mx-auto px-4 flex justify-center md:block">
                        <h1 className="text-center md:text-left text-4xl md:text-6xl font-bold text-white mb-4 drop-shadow-[0_2px_8px_rgba(0,0,0,0.8)]">
                            {pageData.title}
                        </h1>
                    </div>

                </section >
            )
            }

            <>
                <section className="bg-gradient-to-r from-secondary to-primary py-6 hidden sm:block">
                    <div className="container mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-y-6 text-white px-4">
                        {aboutPages.map(page => (
                            <div key={page.id} className="flex items-center gap-2 mb-1 group">
                                <span className="text-lg transform transition-transform group-hover:translate-x-1">
                                    <ChevronRight className="w-5 h-5" />
                                </span>
                                <Link
                                    href={`/about/${page.slug}`}
                                    className="text-base font-semibold px-1 py-1 rounded-md transition-colors hover:bg-secondary"
                                >
                                    {page.title}
                                </Link>
                            </div>
                        ))}
                    </div>
                </section>

                <section className="sm:hidden">
                    <div className="container mx-auto px-4 mt-2">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button
                                    className="w-full flex justify-between items-center text-white text-sm font-semibold py-2 px-3 sm:text-base bg-secondary rounded-md"
                                    aria-label="Toggle About Us Pages dropdown"
                                >
                                    About Us Pages
                                    <ChevronDown className="w-5 h-5" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-secondary border-0 p-0 mt-1 rounded-md shadow-lg w-[calc(100vw-2rem)] max-w-md">
                                {aboutPages.map((page) => (
                                    <DropdownMenuItem asChild key={page.id} className="hover:bg-primary">
                                        <Link
                                            href={`/about/${page.slug}`}
                                            className="flex items-center gap-2 px-4 py-2 text-white font-semibold text-sm"
                                        >
                                            <ChevronRight className="w-4 h-4" />
                                            {page.title}
                                        </Link>
                                    </DropdownMenuItem>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </section>
            </>

            <div className='container mx-auto px-4'>
                <HtmlContentDisplay
                    htmlContent={pageData.content}
                />
                {/* <div dangerouslySetInnerHTML={{ __html: pageData.content }} /> */}
            </div>
        </>
    );
}
