'use client';

import { IHomeTrustedPartner } from '@/types/home';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const ApprovedSuppliersSection = ({ homeTrustedPartner }: { homeTrustedPartner?: IHomeTrustedPartner }) => {

  const slidesToShow = 3;
  const images = homeTrustedPartner?.images ?? [];
  const maxIndex = Math.max(images.length - slidesToShow, 0);

  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev >= maxIndex ? 0 : prev + 1));
  }, [maxIndex]);

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev <= 0 ? maxIndex : prev - 1));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide();
    }, 4000);
    return () => clearInterval(interval);
  }, [nextSlide]);

  return (
    <section className="py-8 sm:py-10 md:py-12 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6 lg:gap-6">
          <div className="w-full lg:w-1/2">
            <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-700 leading-relaxed">
              {homeTrustedPartner?.description}
            </h2>
          </div>

          <div className="lg:w-1/2 relative overflow-hidden">
            <div
              className="flex transition-transform duration-700 ease-in-out gap-x-2"
              style={{ transform: `translateX(-${(100 / slidesToShow) * currentSlide}%)` }}
            >
              {images.map((logo, index) => (
                <div key={index} className="min-w-[33.3333%] flex justify-center p-4">
                  <Image
                    src={logo}
                    alt={`Approved supplier logo ${index + 1}`}
                    width={200}
                    height={400}
                    className="hover:scale-105 object-contain transition-transform duration-200"
                  />
                </div>
              ))}
            </div>

            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 backdrop-blur-sm p-1 rounded-full"
              aria-label="Previous slide"
            >
              <ChevronLeft className="h-6 w-6 text-gray-800" />
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 -translate-y-1/2 bg-white/30 hover:bg-white/50 backdrop-blur-sm p-1 rounded-full"
              aria-label="Next slide"
            >
              <ChevronRight className="h-6 w-6 text-gray-800" />
            </button>

            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
              {Array.from({ length: maxIndex + 1 }).map((_, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentSlide(idx)}
                  className={`h-2 rounded-full transition-all ${currentSlide === idx ? 'w-12 bg-gray-700' : 'w-2 bg-gray-400/50'
                    }`}
                  aria-label={`Go to slide set ${idx + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ApprovedSuppliersSection;
