import Link from "next/link";
import type { Metadata } from "next";

import getSubCategory from "@/actions/subcategory/get-subcategory";
import getProduct from "@/actions/product/get-product";

import type { ISubCategory, IProduct } from "@/types/category";
import { ChevronRight, Plane } from "lucide-react";

export const revalidate = 0;

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Aircraft | Mustang Airworks",
    description: "Explore fixed-wing aircraft and helicopters.",
  };
}

function cardVisualsFor(_sub: ISubCategory) {
  console.log(_sub);
  return {
    image: _sub.image || "/images/helicopter.png",
    gradient: "from-black/60 via-black/40 to-black/20",
    Icon: Plane,
    titleColor: "text-white",
  };
}

export default async function AirCraftSection() {
  const [subRes, prodRes] = await Promise.all([getSubCategory(), getProduct()]);
  const subcategories: ISubCategory[] = subRes?.data ?? [];
  const products: IProduct[] = prodRes?.data ?? [];

  const allowedSubs = new Set(["fixed-wing-aircraft", "helicopters"]);
  const subsForPage = subcategories.filter((s) => allowedSubs.has(s.slug));

  const productsBySub: Record<string, IProduct[]> = {};
  for (const p of products) {
    if (!p?.subCategoryId) continue;
    (productsBySub[p.subCategoryId] ??= []).push(p);
  }

  const groups = subsForPage
    .map((sub) => ({
      sub,
      items: (sub.products ?? []).filter((p) => p.name && p.slug),
    }))
    .filter((g) => g.items.length > 0);

  return (
    <section className="py-14 bg-gradient-to-br bg-gray-100 relative overflow-hidden">
      <div className="absolute inset-0 opacity-5">
        <div
          className="absolute top-0 left-0 w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="mb-10 text-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
            AIRCRAFT SUPPORT
          </h1>
          <p className="text-gray-600 mt-2 text-sm sm:text-base">
            Comprehensive parts and maintenance support for airlines and
            helicopters across Nepal and South Asia
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-6 rounded-full" />
        </div>

        <div className="grid gap-12 lg:grid-cols-2 mb-4 max-w-7xl mx-auto">
          {groups.map(({ sub, items }) => {
            const { image, gradient, Icon, titleColor } = cardVisualsFor(sub);

            return (
              <article
                key={sub.id}
                className="group relative overflow-hidden rounded-3xl bg-white shadow-lg transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl"
              >
                <div className="relative h-80 overflow-hidden">
                  <div
                    className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-110"
                    style={{ backgroundImage: `url(${image})` }}
                  />
                  <div
                    className={`absolute inset-0 bg-gradient-to-t ${gradient}`}
                  />
                  <div className="absolute bottom-6 left-6 flex items-center gap-3">
                    <div className="w-14 h-14 bg-white/25 backdrop-blur-md rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform">
                      <Icon className="w-7 h-7 text-primary" />
                    </div>
                    <h3
                      className={`text-3xl font-bold drop-shadow-lg ${titleColor}`}
                    >
                      {sub.name}
                    </h3>
                  </div>
                </div>

                <div className="bg-white p-4">
                  <ul className="space-y-1">
                    {items.map((p, index) => (
                      <li
                        key={p.id}
                        style={{ transitionDelay: `${index * 50}ms` }}
                        className="transition-all"
                      >
                        <Link
                          href={`/aircraft/${p.slug}`}
                          className="group/item flex items-center justify-between py-2 px-2 rounded-xl hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 transition-all"
                        >
                          <div className="flex items-center">
                            <ChevronRight className="w-4 h-4 text-primary mr-3 group-hover/item:translate-x-1 transition-transform" />
                            <span className="font-medium text-gray-700 group-hover/item:text-primary transition-colors">
                              {p.name}
                            </span>
                          </div>
                          <div className="w-2 h-2 bg-primary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity" />
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </article>
            );
          })}
        </div>
      </div>
    </section>
  );
}



// 'use client';

// import { useState } from 'react';
// import Link from 'next/link';
// import { ChevronRight, Plane, RotateCcw } from 'lucide-react';

// const AircraftSection = () => {
//   const [hoveredCard, setHoveredCard] = useState<string | null>(null);

//   const aircraftData = {
//     airline: {
//       title: "AIRLINE",
//       icon: Plane,
//       image: "/images/airline.png",
//       // Use gradient blending from your secondary navy with slight teal tones for smoothness
//       gradient: "from-[rgba(30,64,175,0.85)] via-[rgba(30,64,175,0.7)] to-[rgba(30,64,175,0.5)]",
//       aircraft: [
//         { name: "AIRBUS A330", href: "/aircraft/airbus-a330" },
//         { name: "AIRBUS A320", href: "/aircraft/airbus-a320" },
//         { name: "ATR 72", href: "/aircraft/atr-72" },
//         { name: "BOMBARDIER DASH Q400", href: "/aircraft/bombardier-dash-q400" },
//         { name: "CRJ 200/700", href: "/aircraft/crj-200-700" },
//         { name: "LET410", href: "/aircraft/let410" },
//         { name: "TWIN-OTTER ", href: "/aircraft/twin-otter" },
//       ]
//     },
//     helicopter: {
//       title: "HELICOPTER",
//       icon: RotateCcw,
//       image: "/images/helicopter.png",
//       // Gradient blending using your primary and secondary colors lightly for freshness
//       gradient: "from-[rgba(30,64,175,0.85)] via-[rgba(45,118,232,0.7)] to-[rgba(229,62,62,0.4)]",
//       aircraft: [
//         { name: "AIRBUS AS350(H125)", href: "/aircraft/airbus-as350" },
//         { name: "AIRBUS H130", href: "/aircraft/airbus-h130" },
//         { name: "BELL 407", href: "/aircraft/bell-407" },
//         { name: "BELL 505", href: "/aircraft/bell-505" },
//         { name: "AGUSTA WESTLAND AW-139S", href: "/aircraft/agusta-aw139s" }
//       ]
//     }
//   };

//   const renderCard = (key: 'airline' | 'helicopter') => {
//     const data = aircraftData[key];
//     const Icon = data.icon;

//     return (
//       <div
//         className={`group relative overflow-hidden rounded-3xl bg-white shadow-lg transition-all duration-500 transform ${hoveredCard === key ? 'scale-105 shadow-2xl ring ring-primary' : ''
//           }`}
//         onMouseEnter={() => setHoveredCard(key)}
//         onMouseLeave={() => setHoveredCard(null)}
//       >
//         {/* Image Header */}
//         <div className="relative h-80 overflow-hidden">
//           <div
//             className="absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-110"
//             style={{ backgroundImage: `url(${data.image})` }}
//           />

//           {/* Title & Icon */}
//           <div className="absolute bottom-6 left-6 flex items-center gap-3">
//             <div className="w-14 h-14 bg-white/25 backdrop-blur-md rounded-full flex items-center justify-center shadow-md group-hover:scale-110 transition-transform">
//               <Icon className="w-7 h-7 text-primary" />
//             </div>
//             <h3 className="text-3xl font-bold text-primary drop-shadow-lg">{data.title}</h3>
//           </div>
//         </div>

//         {/* Aircraft List */}
//         <div className="bg-white p-4">
//           <ul className="space-y-1">
//             {data.aircraft.map((aircraft, index) => (
//               <li key={index} className="transition-all delay-[calc(index*50ms)]">
//                 <Link
//                   href={aircraft.href}
//                   className="group/item flex items-center justify-between py-2 px-2 rounded-xl hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 transition-all"
//                 >
//                   <div className="flex items-center">
//                     <ChevronRight className="w-4 h-4 text-primary mr-3 group-hover/item:translate-x-1 transition-transform" />
//                     <span className="font-medium text-gray-700 group-hover/item:text-primary transition-colors">
//                       {aircraft.name}
//                     </span>
//                   </div>
//                   <div className="w-2 h-2 bg-primary rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity" />
//                 </Link>
//               </li>
//             ))}
//           </ul>
//         </div>
//       </div>
//     );
//   };

//   return (
//     <section className="py-14 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
//       {/* Background Pattern */}
//       <div className="absolute inset-0 opacity-5">
//         <div
//           className="absolute top-0 left-0 w-full h-full"
//           style={{
//             backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/svg%3E")`
//           }}
//         />
//       </div>

//       <div className="container mx-auto px-4 relative z-10">
//         {/* Section Header */}
//         <div className="text-center mb-16">
//           <h2 className="text-4xl font-bold bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent mb-4">
//             AIRCRAFT SUPPORT
//           </h2>
//           <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
//             Comprehensive parts and maintenance support for airlines and helicopters across Nepal and South Asia
//           </p>
//           <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-6 rounded-full" />
//         </div>

//         {/* Cards */}
//         <div className="grid lg:grid-cols-2 gap-12 mb-16">
//           {renderCard('airline')}
//           {renderCard('helicopter')}
//         </div>

//         {/* CTA */}
//         {/* <div className="text-center">
//           <Link
//             href="/aircraft"
//             className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold text-lg rounded-full hover:from-[#c22d2d] hover:to-[#193b88] transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
//           >
//             <span>Explore All Aircraft</span>
//             <ChevronRight className="w-5 h-5 ml-2" />
//           </Link>
//         </div> */}
//       </div>
//     </section>
//   );
// };

// export default AircraftSection;
