"use client";

import { Skeleton } from "@/components/ui/skeleton";

export default function AppSkeleton() {
  return (
    <div className="min-h-screen bg-background">
      {/* Navbar skeleton */}
      <header className="border-b bg-[#F9FAFB]">
        <div className="container mx-auto px-3 sm:px-4 py-3 md:py-6 flex items-center gap-3 sm:gap-6">
          <Skeleton className="h-10 w-28 rounded" /> {/* logo */}
          <div className="hidden md:block flex-1">
            <Skeleton className="h-5 w-80" />
            <div className="mt-3 flex gap-3">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-4 w-40" />
            </div>
          </div>
          <div className="ml-auto hidden lg:flex items-center gap-3">
            <Skeleton className="h-9 w-64" /> {/* search */}
            <Skeleton className="h-9 w-32" /> {/* CTA */}
          </div>
          <div className="lg:hidden ml-auto">
            <Skeleton className="h-8 w-8 rounded" />
          </div>
        </div>
        <div className="hidden lg:block bg-secondary">
          <div className="container mx-auto px-4 h-12 flex items-center gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-4 w-20" />
            ))}
            <div className="ml-auto">
              <Skeleton className="h-7 w-36" />
            </div>
          </div>
        </div>
      </header>

      {/* Hero skeleton */}
      <section className="relative">
        <Skeleton className="h-[220px] sm:h-[300px] md:h-[420px] w-full rounded-none" />
      </section>

      {/* Content blocks */}
      <main className="container mx-auto px-3 sm:px-4 py-8 sm:py-10">
        {/* Section title */}
        <div className="mb-6 sm:mb-8">
          <Skeleton className="h-7 w-48" />
          <div className="mt-3 flex gap-3">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>

        {/* Cards grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="rounded-lg border p-4">
              <Skeleton className="h-40 w-full mb-4" />
              <Skeleton className="h-5 w-3/5 mb-2" />
              <Skeleton className="h-4 w-4/5" />
            </div>
          ))}
        </div>

        {/* Accordions / text list */}
        <div className="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array.from({ length: 2 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="h-6 w-48" />
              {Array.from({ length: 4 }).map((__, k) => (
                <div key={k} className="rounded-md border p-3">
                  <Skeleton className="h-5 w-2/3 mb-2" />
                  <Skeleton className="h-4 w-full" />
                </div>
              ))}
            </div>
          ))}
        </div>
      </main>

      {/* Footer skeleton */}
      <footer className="mt-8 border-t">
        <div className="container mx-auto px-3 sm:px-4 py-6 grid grid-cols-2 sm:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i}>
              <Skeleton className="h-5 w-28 mb-3" />
              {Array.from({ length: 3 }).map((__, k) => (
                <Skeleton key={k} className="h-4 w-24 mb-2" />
              ))}
            </div>
          ))}
        </div>
      </footer>
    </div>
  );
}
